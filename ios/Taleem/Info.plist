<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Taleem</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.128323862743-vjdc9isgv7qdt0orq2fbeu2mt2hgo141</string>
			</array>
		</dict>
		<dict/>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tel</string>
		<string>mailto</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MKDirectionsApplicationSupportedModes</key>
	<array/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Taleem needs camera access to let you upload profile photos, documents, and participate in video classes.</string>
	<key>NSContactsUsageDescription</key>
	<string>Taleem requires access to your contacts to enable connecting with your friends for classes.</string>
	<key>NSDocumentTypeUsageDescription</key>
	<string>Taleem requires access to your documents to enable uploading files for your profile or settings.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Taleem requires access to your location to enable location-based services for connecting students and tutors for in-person classes.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Taleem requires access to your location to enable location-based services for connecting students and tutors for in-person classes. By allowing location access, tutors can share their location to indicate where they offer face-to-face sessions, and students can share their location to request tutors to visit their preferred location for personalized learning.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Taleem requires access to your microphone to enable audio calls and live interactive streaming with tutors and other students.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Taleem requires access to your photo library to enable uploading profile pictures and documents.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Taleem requires access to your photo library to enable uploading profile pictures and documents.</string>
	<key>UIAppFonts</key>
	<array>
		<string>metropolis-black.ttf</string>
		<string>metropolis-bold.ttf</string>
		<string>metropolis-extraBold.ttf</string>
		<string>metropolis-medium.ttf</string>
		<string>metropolis-semiBold.ttf</string>
		<string>metropolis-thin.ttf</string>
		<string>Poppins-Regular.ttf</string>
	</array>

	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>

	<key>LSSupportsOpeningDocumentsInPlace</key>
<true/>
<key>UIFileSharingEnabled</key>
<true/>
</dict>
</plist>
