import React, {useEffect, useState} from 'react';
import {AppState, StyleSheet, Text, View} from 'react-native';
import {enableScreens} from 'react-native-screens';
import Toast, {BaseToast, ErrorToast} from 'react-native-toast-message';
import {Provider, useSelector} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import colors from './Src/Utils/colors';
import {store, persistor} from './Src/Store/Store';
import {NavigationContainer} from '@react-navigation/native';
import EntryStack from './Src/Navigation/EntryStack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {WAS_KILLED} from './Src/Utils/storageKeys';
import {I18nInitializer} from './Src/Config/i18next';
import {requestUserPermission} from './Src/Helper/Notifications/NotiHelper';
import {notificationListener} from './Src/Helper/NotiHelper/NotiHelper';
import {fp, hp, wp} from './Src/Helper/ResponsiveDimensions';
import {Fonts} from './Src/Utils/Fonts';
import WhiteBoardScoket from './Src/Screens/TestScreen/WhiteBoardSocket';
import WhiteBoard from './Src/Screens/StartClass/WhiteBoard';
import StartClassScreen from './Src/Screens/StartClass';
import {SocketProvider} from './Src/Helper/SocketHelper/SocketProvider';

const App = () => {
  useEffect(() => {
    requestUserPermission();
    notificationListener();
  }, []);
  return (
    <>
      <SocketProvider>
        <Provider store={store}>
          <I18nInitializer>
            <PersistGate loading={null} persistor={persistor}>
              <NavigationContainer>
                <EntryStack />
                {/* <WhiteBoard /> */}
                {/* <StartClassScreen /> */}
              </NavigationContainer>
            </PersistGate>
          </I18nInitializer>
        </Provider>
      </SocketProvider>
      <Toast
        config={{
          success: props => (
            <BaseToast
              {...props}
              style={{
                borderLeftColor: colors.themeColor,
                borderColor: colors.themeColor,
                borderWidth: 1,
                paddingVertical: 10,
                paddingHorizontal: 15,
                width: '90%',
                shadowColor: colors.black,
                shadowOpacity: 0.2,
                shadowRadius: 8,
                shadowOffset: {width: 0, height: 2},
              }}
              contentContainerStyle={{paddingHorizontal: 15}}
              text1Style={{
                fontSize: 14,
                fontWeight: 'bold',
                color: colors.black,
                textAlign: 'left',
                flexWrap: 'wrap', // Allow text to wrap
              }}
              text2Style={{
                fontSize: 12,
                color: colors.grey,
                textAlign: 'left',
                flexWrap: 'wrap', // Allow text to wrap
              }}
            />
          ),
          error: props => (
            <ErrorToast
              {...props}
              text1Style={{
                fontSize: 18,
              }}
              text2Style={{
                fontSize: 14,
              }}
              style={{
                height: 'auto',
                paddingVertical: 10,
                paddingHorizontal: 0,
              }}
              // style={{
              //   borderLeftColor: colors.orangeLight,
              //   borderColor: colors.orangeLight,
              //   borderWidth: 1,
              //   paddingVertical: 10,
              //   paddingHorizontal: 15,
              //   width: '90%',
              //   shadowColor: colors.black,
              //   shadowOpacity: 0.2,
              //   shadowRadius: 8,
              //   shadowOffset: {width: 0, height: 2},
              // }}
              // contentContainerStyle={{paddingHorizontal: 15}}
              // text1Style={{
              //   fontSize: 14,
              //   fontWeight: 'bold',
              //   color: colors.black,
              //   textAlign: 'left',
              //   flexWrap: 'wrap', // Allow text to wrap
              //   lineHeight: 20,
              // }}
              // text2Style={{
              //   fontSize: 12,
              //   color: colors.grey,
              //   textAlign: 'left',
              //   flexWrap: 'wrap', // Allow text to wrap
              //   lineHeight: 20,
              // }}
            />
          ),
          tomatoToast: ({props}) => {
            return (
              <View style={props.style}>
                <Text style={props.textStyle}>{props.text}</Text>
              </View>
            );
          },
        }}
      />
    </>
  );
};

enableScreens();

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
