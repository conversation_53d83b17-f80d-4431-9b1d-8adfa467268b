import messaging from '@react-native-firebase/messaging';
import notifee, {AndroidImportance} from '@notifee/react-native';
import {Platform, AppState} from 'react-native';
import {store} from '../../Store/Store'; // Ensure this is the correct store import
import apiSlice from '../../Api/ApiSlice';
import {
  setUnreadCount,
  setUnreadMessageCount,
  setUnreadSupportMessageCount,
} from '../../Redux/Slices/NotiSlice/NotificationSlice';

// Track processed messages to prevent duplicates
const processedMessages = new Set();

// Separate function to update unread counts
function updateUnreadCounts() {
  store
    .dispatch(
      apiSlice.endpoints.getUnreadNotiCount.initiate(undefined, {
        forceRefetch: true,
      }),
    )
    .unwrap()
    .then(data => {
      const {unreadNotificationCount, unreadChatCount, unreadSuportMessages} =
        data.data;
      store.dispatch(setUnreadCount(unreadNotificationCount || 0));
      store.dispatch(setUnreadMessageCount(unreadChatCount || 0));
      store.dispatch(setUnreadSupportMessageCount(unreadSuportMessages || 0));
    })
    .catch(err => {
      console.error('Error fetching unread counts:', err);
    });
}

// Handle foreground notifications (display + update counts)
function handleForegroundMessage(message) {
  console.log('🚀 ~ handleForegroundMessage ~ message:', message);

  if (!message) return;

  // Create unique message ID for deduplication
  const messageId =
    message.messageId ||
    `${message.notification?.title}-${
      message.notification?.body
    }-${Date.now()}`;

  if (processedMessages.has(messageId)) {
    console.log('Message already processed, skipping:', messageId);
    return;
  }

  processedMessages.add(messageId);

  // Clean up old message IDs (keep only last 50)
  if (processedMessages.size > 50) {
    const oldestIds = Array.from(processedMessages).slice(0, 10);
    oldestIds.forEach(id => processedMessages.delete(id));
  }

  // Display notification only in foreground
  if (AppState.currentState === 'active') {
    if (Platform.OS === 'ios') {
      notifee.displayNotification(message.notification);
    } else {
      notifee.displayNotification({
        title: message?.notification?.title,
        body: message?.notification?.body,
        android: {
          channelId: 'channel_id',
          importance: AndroidImportance.HIGH,
        },
      });
    }
  }

  // Update unread counts
  updateUnreadCounts();
}

// Handle background notifications (only update counts, don't display)
function handleBackgroundMessage(message) {
  console.log('🚀 ~ handleBackgroundMessage ~ message:', message);

  if (!message) return;

  // Create unique message ID for deduplication
  const messageId =
    message.messageId ||
    `${message.notification?.title}-${
      message.notification?.body
    }-${Date.now()}`;

  if (processedMessages.has(messageId)) {
    console.log('Background message already processed, skipping:', messageId);
    return;
  }

  processedMessages.add(messageId);

  // Only update counts in background, don't display notification
  // (Android system will handle the display)
  updateUnreadCounts();
}

// Handle notification tap (only update counts, don't display)
function handleNotificationOpened(message) {
  console.log('🚀 ~ handleNotificationOpened ~ message:', message);

  if (!message) return;

  // Don't display notification when opened, just update counts
  updateUnreadCounts();
}

export const notificationListener = async () => {
  // Handle app opened from quit state
  messaging().getInitialNotification().then(handleNotificationOpened);

  // Handle foreground messages
  messaging().onMessage(handleForegroundMessage);

  // Handle notification tap when app is in background
  messaging().onNotificationOpenedApp(handleNotificationOpened);

  // Handle background messages (Android)
  messaging().setBackgroundMessageHandler(async message => {
    console.log('Background Notification:', message);
    handleBackgroundMessage(message);
  });
};
