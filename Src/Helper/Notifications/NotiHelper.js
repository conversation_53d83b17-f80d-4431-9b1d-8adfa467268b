import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import notifee, {AndroidImportance} from '@notifee/react-native';
import {Alert, Linking, Platform} from 'react-native';
import {FCM_TOKEN} from '../../Utils/storageKeys';
async function createChannel() {
  await notifee.createChannel({
    id: 'channel_id',
    name: 'Rewards Notifications',
    lights: false,
    vibration: true,
    importance: AndroidImportance.HIGH,
  });
}
export async function requestUserPermission() {
  let permissionGranted = false;
  if (Platform.OS === 'ios') {
    const notifeeStatus = await notifee.requestPermission();
    const authStatus = await messaging().requestPermission();
    permissionGranted =
      notifeeStatus?.authorizationStatus === 1 ||
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
  } else if (Platform.OS === 'android') {
    if (Platform.Version >= 33) {
      const notifeeStatus = await notifee.requestPermission();
      permissionGranted = notifeeStatus?.authorizationStatus === 1;
    } else {
      permissionGranted = true; // Permissions are granted by default below Android 13
    }
  }

  if (permissionGranted) {
    console.log('Notification permission granted');
    await createChannel();
    uploadFcmToken();
  } else {
    console.log('Notification permission denied');
    showSettingsAlert();
  }
}

function showSettingsAlert() {
  Alert.alert(
    'Permission Required',
    'Notification permission is required to receive important updates. Please enable it in settings.',
    [
      {text: 'Cancel', style: 'cancel'},
      {
        text: 'Open Settings',
        onPress: () => Linking.openSettings(),
      },
    ],
    {cancelable: true},
  );
}

export const uploadFcmToken = async () => {
  console.log('upload fcm token');
  try {
    const fcmToken = await messaging().getToken();
    if (fcmToken) {
      console.log('🚀 ~ uploadFcmToken ~ fcmToken:', fcmToken);
      await AsyncStorage.setItem(FCM_TOKEN, fcmToken);
    }
  } catch (error) {
    console.error('Failed to upload FCM token:', error);
    // showSettingsAlert();
    return null;
  }
};

// NotificationServices function removed to prevent duplicate listeners
// All notification handling is now consolidated in NotiHelper.js
